<template>
  <!-- 首页容器 -->
  <view class="content">
    <!-- 应用Logo -->
    <image class="logo" src="/static/logo.png"></image>
    <!-- 标题区域 -->
    <view class="text-area">
      <text class="title">{{title}}</text>
    </view>
    
    <!-- 主题切换选择器 -->
    <view class="theme-selector-container">
      <select @change="onThemeChange" class="theme-selector">
        <option value="default">默认主题</option>
        <option value="dark">暗色主题</option>
        <option value="green">绿色主题</option>
        <option value="red">紫色主题</option>
      </select>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import themeManager from '../../utils/theme.js';

// 页面标题
const title = ref('首页');

// 页面加载生命周期钩子
onMounted(() => {
  // 页面加载逻辑可以在这里添加
});
</script>

<script>
// 主题切换事件处理
function onThemeChange(event) {
  const theme = event.target.value;
  // 使用主题管理工具切换主题
  themeManager.switchTheme(theme);
}
</script>

<style>
/* 页面内容容器 */
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  min-height: 100vh;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(135deg, var(--primary-color, #3498db), var(--secondary-color, #2c3e50));
  position: relative;
}

/* 装饰元素 */
.content::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* Logo样式 */
.logo {
  height: 200rpx;
  width: 200rpx;
  margin: 40rpx;
  border-radius: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  background-color: var(--bg-white, #ffffff);
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color, #3498db);
  font-size: 80rpx;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* 文本区域 */
.text-area {
  display: flex;
  justify-content: center;
  margin: 40rpx;
  position: relative;
  z-index: 1;
}

/* 页面标题 */
.title {
  font-size: 36rpx;
  color: var(--text-white, #ffffff);
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 主题切换容器 */
.theme-selector-container {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

/* 主题选择器 */
.theme-selector {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  border-radius: 50rpx;
  border: none;
  background-color: var(--bg-white, rgba(255, 255, 255, 0.9));
  color: var(--text-color, #2c3e50);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-weight: 500;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15rpx center;
  background-size: 18rpx;
  padding-right: 50rpx;
  cursor: pointer;
}

/* 主题选择器悬停效果 */
.theme-selector:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 主题选择器选项样式 */
.theme-selector option {
  padding: 10rpx;
  font-weight: 500;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 768px) {
  .content {
    padding: 20rpx;
  }
  
  .logo {
    height: 150rpx;
    width: 150rpx;
  }
  
  .title {
    font-size: 32rpx;
  }
}
</style>