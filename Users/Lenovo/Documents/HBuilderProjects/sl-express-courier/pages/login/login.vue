<template>
  <!-- 登录页面容器 -->
  <view class="login-container">
    <!-- 登录页面头部 -->
    <view class="login-header">
      <!-- 应用Logo -->
      <image class="logo" src="/static/logo.png"></image>
      <!-- 应用标题 -->
      <text class="app-title">快递员端</text>
    </view>
    
    <!-- 登录方式切换标签 -->
    <view class="login-tabs">
      <!-- 账号登录标签 -->
      <text 
        :class="{ active: activeTab === 'account' }" 
        @click="switchToAccount"
        class="tab-item"
      >
        账号登录
      </text>
      <!-- 手机号登录标签 -->
      <text 
        :class="{ active: activeTab === 'phone' }" 
        @click="switchToPhone"
        class="tab-item"
      >
        手机号登录
      </text>
    </view>
    
    <!-- 账号登录表单 -->
    <view v-if="activeTab === 'account'" class="login-form">
      <!-- 用户名输入框组 -->
      <view class="input-group">
        <input 
          type="text" 
          v-model="account.username" 
          placeholder="请输入账号" 
          class="login-input"
        />
      </view>
      
      <!-- 密码输入框组 -->
      <view class="input-group password-group">
        <input 
          :type="showPassword ? 'text' : 'password'" 
          v-model="account.password" 
          placeholder="请输入密码" 
          class="login-input"
          @keyup.enter="handleAccountLogin"
        />
        <!-- 密码可见性切换图标 -->
        <view 
          @click="togglePasswordVisibility" 
          :class="['eye-icon', { closed: !showPassword }]"
        ></view>
      </view>
      
      <!-- 登录按钮 -->
      <button 
        :disabled="!isValidAccountLogin" 
        @click="handleAccountLogin"
        class="login-button"
      >
        登录
      </button>
      
      <!-- 账号登录错误信息 -->
      <text v-if="accountError" class="error-message">{{ accountError }}</text>
    </view>
    
    <!-- 手机号登录表单 -->
    <view v-else class="login-form">
      <!-- 手机号输入框组 -->
      <view class="input-group">
        <input 
          type="text" 
          v-model="phone.number" 
          placeholder="请输入手机号" 
          maxlength="11"
          class="login-input"
        />
      </view>
      
      <!-- 验证码输入框组 -->
      <view class="input-group verification-group">
        <input 
          type="text" 
          v-model="phone.code" 
          placeholder="请输入验证码" 
          maxlength="6"
          class="login-input verification-input"
        />
        <!-- 获取验证码按钮 -->
        <button 
          :disabled="countdown > 0" 
          @click="getCode"
          class="verification-button"
        >
          {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
        </button>
      </view>
      
      <!-- 登录按钮 -->
      <button 
        :disabled="!isValidPhoneLogin" 
        @click="handlePhoneLogin"
        class="login-button"
      >
        登录
      </button>
      
      <!-- 手机号登录错误信息 -->
      <text v-if="phoneError" class="error-message">{{ phoneError }}</text>
    </view>
    
    <!-- 主题切换选择器 -->
    <view class="theme-selector-container">
      <select @change="onThemeChange" class="theme-selector">
        <option value="default">默认主题</option>
        <option value="dark">暗色主题</option>
        <option value="green">绿色主题</option>
        <option value="red">紫色主题</option>
      </select>
    </view>
  </view>
</template>

<script>
// 导入登录服务和验证工具
import { LoginService, ValidationUtil } from './login.js';
// 导入主题管理工具
import themeManager from '../../utils/theme.js';

export default {
  // 页面数据
  data() {
    return {
      activeTab: 'account', // 当前激活的登录标签页 ('account' 或 'phone')
      showPassword: false,  // 密码是否可见
      countdown: 0,         // 验证码倒计时
      // 账号登录表单数据
      account: {
        username: '',
        password: ''
      },
      // 手机号登录表单数据
      phone: {
        number: '',
        code: ''
      },
      accountError: '',     // 账号登录错误信息
      phoneError: ''        // 手机号登录错误信息
    }
  },
  // 计算属性
  computed: {
    // 验证账号登录表单是否有效
    isValidAccountLogin() {
      return this.account.username && this.account.password
    },
    // 验证手机号登录表单是否有效
    isValidPhoneLogin() {
      return /^1[3-9]\d{9}$/.test(this.phone.number) && /^\d{6}$/.test(this.phone.code)
    }
  },
  // 页面加载时执行
  mounted() {
    // 初始化主题
    themeManager.init();
  },
  // 页面方法
  methods: {
    /**
     * 切换到账号登录标签页
     */
    switchToAccount() {
      this.activeTab = 'account'
    },
    
    /**
     * 切换到手机号登录标签页
     */
    switchToPhone() {
      this.activeTab = 'phone'
    },
    
    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },
    
    /**
     * 处理账号登录
     */
    async handleAccountLogin() {
      // 验证表单
      const validation = ValidationUtil.validateAccountForm(this.account);
      if (!validation.isValid) {
        this.accountError = validation.errors[0];
        return;
      }
      
      // 清除错误信息
      this.accountError = '';
      
      try {
        // 显示加载提示
        uni.showLoading({
          title: '登录中...'
        });
        
        // 调用登录服务
        const result = await LoginService.accountLogin(
          this.account.username,
          this.account.password
        );
        
        // 隐藏加载提示
        uni.hideLoading();
        
        // 显示成功提示
        uni.showToast({
          title: result.message,
          icon: 'success'
        });
        
        // 延迟跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }, 1000);
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误信息
        this.accountError = error.message;
      }
    },
    
    /**
     * 获取验证码
     */
    async getCode() {
      // 检查手机号是否为空
      if (!this.phone.number) {
        this.phoneError = '请输入手机号';
        return;
      }
      
      // 验证手机号格式
      if (!/^1\d{10}$/.test(this.phone.number)) {
        this.phoneError = '请输入正确的手机号';
        return;
      }
      
      // 清除错误信息
      this.phoneError = '';
      
      try {
        // 显示加载提示
        uni.showLoading({
          title: '发送中...'
        });
        
        // 调用发送验证码服务
        const result = await LoginService.sendVerificationCode(this.phone.number);
        
        // 隐藏加载提示
        uni.hideLoading();
        
        // 显示成功提示
        uni.showToast({
          title: result.message,
          icon: 'success'
        });
        
        // 启动倒计时
        this.startCountdown();
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误信息
        this.phoneError = error.message;
      }
    },
    
    /**
     * 启动验证码倒计时
     */
    startCountdown() {
      this.countdown = 60;
      const timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(timer);
        }
      }, 1000);
    },
    
    /**
     * 处理手机号登录
     */
    async handlePhoneLogin() {
      // 验证表单
      const validation = ValidationUtil.validatePhoneForm(this.phone);
      if (!validation.isValid) {
        this.phoneError = validation.errors[0];
        return;
      }
      
      // 清除错误信息
      this.phoneError = '';
      
      try {
        // 显示加载提示
        uni.showLoading({
          title: '登录中...'
        });
        
        // 调用登录服务
        const result = await LoginService.phoneLogin(
          this.phone.number,
          this.phone.code
        );
        
        // 隐藏加载提示
        uni.hideLoading();
        
        // 显示成功提示
        uni.showToast({
          title: result.message,
          icon: 'success'
        });
        
        // 延迟跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          });
        }, 1000);
      } catch (error) {
        // 隐藏加载提示
        uni.hideLoading();
        // 显示错误信息
        this.phoneError = error.message;
      }
    },
    
    /**
     * 主题切换事件处理
     */
    onThemeChange(event) {
      const theme = event.target.value;
      // 使用统一的主题管理器切换主题
      themeManager.switchTheme(theme);
    }
  }
}
</script>

<!-- 引入登录页面样式 -->
<style scoped src="./login.css"></style>