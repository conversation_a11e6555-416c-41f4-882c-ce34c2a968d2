/* 登录标签页容器 */
.login-tabs {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  padding: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1;
}

/* 标签项 */
.tab-item {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
  padding: 25rpx 40rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 60rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
  font-weight: 500;
}

/* 激活的标签项 - 优化字体清晰度 */
.tab-item.active {
  background-color: var(--bg-white);
  color: var(--primary-color);
  font-weight: 700;
  box-shadow: 0 4rpx 10rpx var(--shadow-primary);
  // 增加文字阴影以提高可读性
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}