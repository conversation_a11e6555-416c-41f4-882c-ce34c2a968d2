<style scoped>
.login-container {
  background: linear-gradient(180deg, #46A3FF 0%, #1E5FAF 100%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px; /* 确保在小屏幕上也有适当的内边距 */
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.app-title {
  font-size: 24px;
  color: #FFFFFF;
  font-weight: bold;
}

.login-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.tab-item {
  padding: 10px 20px;
  color: #FFFFFF;
  font-size: 16px;
  cursor: pointer;
  margin: 0 10px;
}

.tab-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.login-form {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 360px;
}

.input-group {
  margin-bottom: 15px;
}

.login-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #CCCCCC;
  border-radius: 4px;
  font-size: 14px;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
}

.login-button {
  width: 100%;
  padding: 10px;
  background-color: #46A3FF;
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.error-message {
  color: #FF4D4F;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}
</style>

<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png"></image>
      <text class="app-title">快递员端</text>
    </view>
    
    <view class="login-tabs">
      <text 
        :class="{ active: activeTab === 'account' }" 
        @click="switchToAccount"
        class="tab-item"
      >
        账号登录
      </text>
      <text 
        :class="{ active: activeTab === 'phone' }" 
        @click="switchToPhone"
        class="tab-item"
      >
        手机号登录
      </text>
    </view>
    
    <view v-if="activeTab === 'account'" class="login-form">
      <!-- ... existing code ... -->
    </view>
    
    <view v-if="activeTab === 'phone'" class="login-form">
      <!-- ... existing code ... -->
    </view>
  </view>
</template>

<script>
</script>