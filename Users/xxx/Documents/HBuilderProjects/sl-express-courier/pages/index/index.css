/* 主题切换容器 - 移动到右上角 */
.theme-selector-container {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 999;
}

/* 主题选择器 */
.theme-selector {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  border-radius: 50rpx;
  border: none;
  background-color: var(--bg-white, rgba(255, 255, 255, 0.9));
  color: var(--text-color, #2c3e50);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-weight: 500;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15rpx center;
  background-size: 18rpx;
  padding-right: 50rpx;
  cursor: pointer;
}

/* 主题选择器悬停效果 */
.theme-selector:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 主题选择器选项样式 */
.theme-selector option {
  padding: 10rpx;
  font-weight: 500;
}