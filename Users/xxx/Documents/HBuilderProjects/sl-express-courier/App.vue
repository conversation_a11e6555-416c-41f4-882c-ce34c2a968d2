<style>
  /* 引入全局主题样式 */
  @import './theme.css';
  
  /* 全局HTML和Body样式重置 */
  html, body {
    margin: 0;              /* 移除默认外边距 */
    padding: 0;             /* 移除默认内边距 */
    overflow: hidden;       /* 隐藏滚动条 */
    width: 100%;            /* 宽度占满屏幕 */
    height: 100%;           /* 高度占满屏幕 */
    box-sizing: border-box; /* 使用边框盒模型 */
  }
  
  /* 隐藏Webkit内核浏览器的滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  
  /* 确保所有元素使用边框盒模型 */
  *, *::before, *::after {
    box-sizing: inherit;
  }
</style>