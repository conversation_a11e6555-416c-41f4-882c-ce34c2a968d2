<template>
  <!-- 登录页面容器 -->
  <view class="login-container">
    <!-- 主题切换选择器 - 放置在右上角 -->
    <view class="theme-selector-container top-right">
      <select @change="onThemeChange" class="theme-selector">
        <option value="default">默认主题</option>
        <option value="dark">暗色主题</option>
        <option value="green">绿色主题</option>
        <option value="red">紫色主题</option>
      </select>
    </view>
    <!-- 应用Logo -->
    <image class="logo" src="/static/logo.png"></image>
    <!-- 应用标题 -->
    <text class="app-title">快递员端</text>
    
    <!-- 登录方式切换标签 -->
    <view class="login-tabs">
      <text :class="{ active: activeTab === 'account' }" @click="switchToAccount">账号登录</text>
      <text :class="{ active: activeTab === 'phone' }" @click="switchToPhone">手机号登录</text>
    </view>
    <!-- 账号登录表单 -->
    <view v-if="activeTab === 'account'" class="login-form">
      <!-- 用户名输入框组 -->
      <view class="input-group">
        <input type="text" v-model="account.username" placeholder="请输入账号" class="login-input"/>
      </view>
      <!-- 密码输入框组 -->
      <view class="input-group password-group">
        <input :type="showPassword ? 'text' : 'password'" v-model="account.password" placeholder="请输入密码" class="login-input"/>
        <view @click="togglePasswordVisibility" class="eye-icon" :class="{ closed: !showPassword }"></view>
      </view>
      <!-- 登录按钮 -->
      <button :disabled="!isValidAccountLogin" @click="handleAccountLogin" class="login-button">登录</button>
      <!-- 账号登录错误信息 -->
      <text v-if="accountError" class="error-message">{{ accountError }}</text>
    </view>
    <!-- 手机号登录表单 -->
    <view v-else class="login-form">
      <!-- 手机号输入框组 -->
      <view class="input-group">
        <input type="text" v-model="phone.number" placeholder="请输入手机号" maxlength="11" class="login-input"/>
      </view>
      <!-- 验证码输入框组 -->
      <view class="input-group verification-group">
        <input type="text" v-model="phone.code" placeholder="请输入验证码" maxlength="6" class="login-input verification-input"/>
        <button :disabled="countdown > 0" @click="getCode" class="verification-button">
          {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
        </button>
      </view>
      <!-- 登录按钮 -->
      <button :disabled="!isValidPhoneLogin" @click="handlePhoneLogin" class="login-button">登录</button>
      <!-- 手机号登录错误信息 -->
      <text v-if="phoneError" class="error-message">{{ phoneError }}</text>
    </view>
  </view>
</template>

<script>
// 添加注释：登录页面逻辑处理
export default {
  data() {
    return {
      activeTab: 'account', // 当前激活的登录方式
      showPassword: false, // 是否显示密码
      countdown: 0, // 验证码倒计时
      account: {
        username: '',
        password: ''
      },
      phone: {
        number: '',
        code: ''
      },
      accountError: '', // 账号登录错误信息
      phoneError: '' // 手机号登录错误信息
    }
  },
  computed: {
    // 添加注释：验证账号登录是否有效
    isValidAccountLogin() {
      return this.account.username.trim() !== '' && this.account.password.trim() !== '';
    },
    // 添加注释：验证手机号登录是否有效
    isValidPhoneLogin() {
      return this.phone.number.trim().length === 11 && this.phone.code.trim().length === 6;
    }
  },
  methods: {
    // 添加注释：切换到账号登录
    switchToAccount() {
      this.activeTab = 'account';
    },
    // 添加注释：切换到手机号登录
    switchToPhone() {
      this.activeTab = 'phone';
    },
    // 添加注释：切换密码可见性
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
    // 添加注释：获取验证码
    getCode() {
      // 模拟获取验证码逻辑
      if (this.phone.number.trim().length !== 11) {
        this.phoneError = '请输入正确的手机号';
        return;
      }
      
      this.phoneError = '';
      this.countdown = 60;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(timer);
          this.countdown = 0;
        }
      }, 1000);
    },
    // 添加注释：处理账号登录
    handleAccountLogin() {
      // 模拟登录逻辑
      if (!this.isValidAccountLogin) {
        this.accountError = '请输入账号和密码';
        return;
      }
      
      // 这里应该调用API进行实际登录
      console.log('账号登录:', this.account);
      this.accountError = '';
    },
    // 添加注释：处理手机号登录
    handlePhoneLogin() {
      // 模拟登录逻辑
      if (!this.isValidPhoneLogin) {
        this.phoneError = '请输入正确的手机号和验证码';
        return;
      }
      
      // 这里应该调用API进行实际登录
      console.log('手机号登录:', this.phone);
      this.phoneError = '';
    },
    // 添加注释：主题切换处理
    onThemeChange(e) {
      const theme = e.target.value;
      // 这里应该调用主题管理器切换主题
      console.log('切换主题:', theme);
    }
  }
}
</script>

<style scoped>
/* 添加注释：主题选择器容器 - 右上角 */
.theme-selector-container.top-right {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}

/* 添加注释：登录页面容器样式 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: var(--bg-color);
  padding: 40rpx;
  box-sizing: border-box;
}

/* 添加注释：应用Logo样式 */
.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

/* 添加注释：应用标题样式 */
.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 60rpx;
}

/* 添加注释：登录方式切换标签样式 */
.login-tabs {
  display: flex;
  width: 100%;
  max-width: 500rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  overflow: hidden;
  margin-bottom: 60rpx;
}

.login-tabs text {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  color: white;
  font-size: 32rpx;
  cursor: pointer;
}

.login-tabs text.active {
  background-color: white;
  color: #007AFF;
}

/* 添加注释：登录表单样式 */
.login-form {
  width: 100%;
  max-width: 500rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

/* 添加注释：输入框组样式 */
.input-group {
  margin-bottom: 30rpx;
  position: relative;
}

/* 添加注释：登录输入框样式 */
.login-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

/* 添加注释：密码输入框组样式 */
.password-group {
  position: relative;
}

/* 添加注释：眼睛图标样式 */
.eye-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zm0 13c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm0-10c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  cursor: pointer;
}

.eye-icon.closed {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zm0 13c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm0-10c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
}

/* 添加注释：验证码输入框组样式 */
.verification-group {
  display: flex;
  gap: 20rpx;
}

/* 添加注释：验证码输入框样式 */
.verification-input {
  flex: 1;
}

/* 添加注释：验证码按钮样式 */
.verification-button {
  padding: 20rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  cursor: pointer;
}

.verification-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 添加注释：登录按钮样式 */
.login-button {
  width: 100%;
  padding: 20rpx;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  cursor: pointer;
  margin-top: 20rpx;
}

.login-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 添加注释：错误信息样式 */
.error-message {
  color: #FF4D4F;
  font-size: 28rpx;
  margin-top: 20rpx;
  text-align: center;
}
</style>