<template>
  <!-- 首页容器 -->
  <view class="content">
    <!-- 主题切换选择器 - 放置在右上角 -->
    <view class="theme-selector-container top-right">
      <select @change="onThemeChange" class="theme-selector">
        <option value="default">默认主题</option>
        <option value="dark">暗色主题</option>
        <option value="green">绿色主题</option>
        <option value="red">紫色主题</option>
      </select>
    </view>
    <!-- 应用Logo -->
    <image class="logo" src="/static/logo.png"></image>
    <!-- 标题区域 -->
    <view class="text-area">
      <text class="title">{{ title }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import themeManager from '../../utils/theme.js';

// 页面标题
const title = ref('首页');

// 页面加载生命周期钩子
onMounted(() => {
  // 初始化主题
  themeManager.init();
});
</script>

<style scoped>
/* 添加注释：主题选择器容器 - 右上角 */
.theme-selector-container.top-right {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}

/* 添加注释：首页内容容器样式 */
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: var(--bg-color);
  padding: 40rpx;
  box-sizing: border-box;
}

/* 添加注释：应用Logo样式 */
.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

/* 添加注释：标题区域样式 */
.text-area {
  text-align: center;
}

/* 添加注释：页面标题样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}
</style>