/**
 * 应用入口文件
 * 负责初始化Vue应用实例
 */

// 导入应用根组件
import App from './App'

// #ifndef VUE3
// Vue 2.x版本初始化代码
import Vue from 'vue'
import './uni.promisify.adaptor'

// 关闭生产环境提示
Vue.config.productionTip = false

// 设置应用类型为小程序
App.mpType = 'app'

// 创建Vue实例
const app = new Vue({
  ...App
})

// 挂载应用
app.$mount()
// #endif

// #ifdef VUE3
// Vue 3.x版本初始化代码
import { createSSRApp } from 'vue'

// 创建应用实例函数
export function createApp() {
  // 创建SSR应用实例
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif