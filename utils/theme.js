/**
 * 主题管理工具类
 * 提供应用程序主题切换和管理功能
 */
class ThemeManager {
  /**
   * 构造函数
   * 初始化主题管理器
   */
  constructor() {
    // 支持的主题列表
    this.themes = ['default', 'dark', 'green', 'red'];
    // 默认主题
    this.currentTheme = 'default';
  }

  /**
   * 切换主题
   * @param {string} theme - 要切换的主题名称
   */
  switchTheme(theme) {
    // 检查主题是否支持
    if (!this.themes.includes(theme)) {
      console.warn(`Theme '${theme}' is not supported.`);
      return;
    }

    // 在uni-app中通过CSS变量方式切换主题，而不是直接操作DOM
    // 移除当前主题类
    const app = getApp();
    if (app && app.$vm && app.$vm.$el) {
      app.$vm.$el.removeAttribute('data-theme');
      
      // 应用新主题
      if (theme !== 'default') {
        app.$vm.$el.setAttribute('data-theme', theme);
      }
    }
    
    // 更新当前主题
    this.currentTheme = theme;
    
    // 保存到本地存储
    try {
      uni.setStorageSync('app-theme', theme);
    } catch (e) {
      console.error('Failed to save theme to storage:', e);
    }
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 初始化主题
   * 从本地存储加载保存的主题
   */
  init() {
    try {
      // 从本地存储获取主题
      const savedTheme = uni.getStorageSync('app-theme') || 'default';
      this.switchTheme(savedTheme);
    } catch (e) {
      console.error('Failed to load theme from storage:', e);
      this.switchTheme('default');
    }
  }

  /**
   * 获取所有支持的主题
   * @returns {Array} 主题列表
   */
  getThemes() {
    return this.themes;
  }
}

// 创建单例实例
const themeManager = new ThemeManager();

// 导出实例
export default themeManager;