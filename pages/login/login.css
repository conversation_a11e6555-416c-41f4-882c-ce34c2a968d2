/* 
 * 登录页面样式文件
 * 定义登录页面使用的所有CSS样式
 */

:root {
  /* 主题颜色 */
  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  --primary-hover: #2980b9;      /* 主色调悬停状态 */
  
  /* 背景颜色 */
  --bg-white: #ffffff;           /* 白色背景 */
  --bg-light: #f8f9fa;           /* 浅色半透明背景 */
  --bg-gray: #bdc3c7;            /* 灰色背景 - 禁用状态按钮 */
  --bg-dark: #ecf0f1;            /* 深灰色背景 */
  
  /* 文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-dark: #2c3e50;          /* 深色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */
  
  /* 边框颜色 */
  --border-color: #dcdde1;       /* 输入框边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
  --shadow-light: rgba(0, 0, 0, 0.1);     /* 浅色阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 登录容器 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  min-height: 100vh;
  padding-top: 100rpx;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

/* 登录容器装饰元素 */
.login-container::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* 登录头部 */
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 1;
}

/* Logo图标 */
.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 30rpx;
  box-shadow: 0 10rpx 30rpx var(--shadow-dark);
  margin-bottom: 30rpx;
  background-color: var(--bg-white);
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 80rpx;
  font-weight: bold;
}

/* 应用标题 */
.app-title {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
}

/* 登录标签页容器 */
.login-tabs {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  padding: 10rpx;
  margin-bottom: 50rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600rpx;
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1;
}

/* 标签项 */
.tab-item {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
  padding: 25rpx 40rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 60rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
  font-weight: 500;
}

/* 激活的标签项 */
.tab-item.active {
  background-color: var(--bg-white);
  color: var(--primary-color);
  font-weight: 700;
  box-shadow: 0 4rpx 10rpx var(--shadow-primary);
}

/* 登录表单 */
.login-form {
  width: 100%;
  max-width: 600rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1;
}

/* 输入框组 */
.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

/* 登录输入框 */
.login-input {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 15rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-white);
  color: var(--text-dark);
  transition: all 0.3s ease;
}

/* 输入框聚焦状态 */
.login-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3rpx var(--shadow-primary);
  outline: none;
}

/* 密码组容器 */
.password-group {
  display: flex;
  align-items: center;
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 25rpx;
  color: var(--primary-color);
  font-size: 26rpx;
  z-index: 2;
  background-color: var(--bg-white);
  padding: 0 10rpx;
  cursor: pointer;
}

/* 眼睛图标容器 */
.eye-icon {
  position: absolute;
  right: 25rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background-color: #FFFFFF;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
}

/* 眼睛图标样式 */
.eye-icon::before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}

/* 眼睛瞳孔 */
.eye-icon::after {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 闭眼图标样式（斜线） */
.eye-icon.closed::before {
  content: "";
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}

.eye-icon.closed::after {
  content: "";
  display: block;
  width: 2rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 0;
}

/* 验证码组容器 */
.verification-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;
}

/* 验证码输入框 */
.verification-input {
  flex: 1;
}

/* 验证码按钮 */
.verification-button {
  width: 200rpx;
  height: 80rpx;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  color: var(--text-white);
  border: none;
  border-radius: 15rpx;
  font-size: 26rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  padding: 0;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 4rpx 10rpx var(--shadow-primary);
  transition: all 0.3s ease;
}

/* 验证码按钮悬停状态 */
.verification-button:hover:not(:disabled) {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 15rpx var(--shadow-primary);
}

/* 验证码按钮禁用状态 */
.verification-button:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  border: none;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: 700;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 15rpx var(--shadow-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

/* 登录按钮悬停状态 */
.login-button:hover:not(:disabled) {
  transform: translateY(-3rpx);
  box-shadow: 0 7rpx 20rpx var(--shadow-dark);
}

/* 登录按钮禁用状态 */
.login-button:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 错误信息 */
.error-message {
  color: var(--text-error);
  font-size: 24rpx;
  margin-top: 20rpx;
  text-align: center;
  font-weight: 500;
}

/* 主题切换容器 */
.theme-selector-container {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

/* 主题选择器 */
.theme-selector {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  border-radius: 50rpx;
  border: none;
  background-color: var(--bg-white);
  color: var(--text-color, #2c3e50);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-weight: 500;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15rpx center;
  background-size: 18rpx;
  padding-right: 50rpx;
  cursor: pointer;
}

/* 主题选择器悬停效果 */
.theme-selector:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 主题选择器选项样式 */
.theme-selector option {
  padding: 10rpx;
  font-weight: 500;
}