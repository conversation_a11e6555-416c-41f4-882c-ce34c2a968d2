if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  class LoginService {
    /**
     * 通用的请求处理方法
     * 根据环境决定使用真实请求还是模拟请求
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 请求结果Promise
     */
    static request(options) {
      {
        return this.mockRequest(options);
      }
    }
    /**
     * 模拟请求处理方法（测试模式）
     * 模拟网络请求，用于开发和测试环境
     * @param {Object} options - 请求配置参数
     * @returns {Promise} 模拟请求结果Promise
     */
    static mockRequest(options) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (options.url === "/api/login/account" && options.method === "POST") {
            const { username, password } = options.data;
            if (username === "test" && password === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1001",
                  username,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "用户名或密码错误"
              });
            }
          } else if (options.url === "/api/login/phone" && options.method === "POST") {
            const { phoneNumber, code } = options.data;
            if (phoneNumber === "***********" && code === "123456") {
              resolve({
                success: true,
                message: "登录成功",
                data: {
                  userId: "1002",
                  phoneNumber,
                  token: "fake_token_string"
                }
              });
            } else {
              reject({
                success: false,
                message: "手机号或验证码错误"
              });
            }
          } else if (options.url === "/api/login/send-code" && options.method === "POST") {
            const { phoneNumber } = options.data;
            if (/^1\d{10}$/.test(phoneNumber)) {
              resolve({
                success: true,
                message: "验证码已发送"
              });
            } else {
              reject({
                success: false,
                message: "手机号格式不正确"
              });
            }
          } else {
            resolve({
              success: true,
              message: "请求成功"
            });
          }
        }, 1e3);
      });
    }
    /**
     * 账号密码登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 登录结果Promise
     */
    static accountLogin(username, password) {
      return this.request({
        url: "/api/login/account",
        method: "POST",
        data: {
          username,
          password
        }
      });
    }
    /**
     * 手机号验证码登录
     * @param {string} phoneNumber - 手机号
     * @param {string} code - 验证码
     * @returns {Promise} 登录结果Promise
     */
    static phoneLogin(phoneNumber, code) {
      return this.request({
        url: "/api/login/phone",
        method: "POST",
        data: {
          phoneNumber,
          code
        }
      });
    }
    /**
     * 发送验证码
     * @param {string} phoneNumber - 手机号
     * @returns {Promise} 发送结果Promise
     */
    static sendVerificationCode(phoneNumber) {
      return this.request({
        url: "/api/login/send-code",
        method: "POST",
        data: {
          phoneNumber
        }
      });
    }
  }
  class ValidationUtil {
    /**
     * 验证手机号格式
     * @param {string} phoneNumber - 手机号
     * @returns {boolean} 是否有效
     */
    static isValidPhoneNumber(phoneNumber) {
      return /^1\d{10}$/.test(phoneNumber);
    }
    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {boolean} 是否有效
     */
    static isValidPassword(password) {
      {
        return password.length >= 1;
      }
    }
    /**
     * 验证验证码格式
     * @param {string} code - 验证码
     * @returns {boolean} 是否有效
     */
    static isValidVerificationCode(code) {
      {
        return code.length === 6;
      }
    }
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱
     * @returns {boolean} 是否有效
     */
    static isValidEmail(email) {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    /**
     * 验证账号密码表单
     * @param {Object} formData - 表单数据 {username, password}
     * @returns {Object} 验证结果 {isValid, errors}
     */
    static validateAccountForm(formData) {
      const errors = [];
      if (!formData.username) {
        errors.push("请输入用户名");
      }
      if (!formData.password) {
        errors.push("请输入密码");
      } else if (!this.isValidPassword(formData.password)) {
        errors.push("密码需包含大小写字母和数字，长度6-20");
      }
      return {
        isValid: errors.length === 0,
        errors
      };
    }
    /**
     * 验证手机号表单
     * @param {Object} formData - 表单数据 {phoneNumber, code}
     * @returns {Object} 验证结果 {isValid, errors}
     */
    static validatePhoneForm(formData) {
      const errors = [];
      if (!formData.phoneNumber) {
        errors.push("请输入手机号");
      } else if (!this.isValidPhoneNumber(formData.phoneNumber)) {
        errors.push("手机号格式不正确");
      }
      if (!formData.code) {
        errors.push("请输入验证码");
      } else if (!this.isValidVerificationCode(formData.code)) {
        errors.push("验证码格式不正确");
      }
      return {
        isValid: errors.length === 0,
        errors
      };
    }
  }
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  class ThemeManager {
    /**
     * 构造函数
     * 初始化主题管理器
     */
    constructor() {
      this.themes = ["default", "dark", "green", "red"];
      this.currentTheme = "default";
    }
    /**
     * 切换主题
     * @param {string} theme - 要切换的主题名称
     */
    switchTheme(theme) {
      if (!this.themes.includes(theme)) {
        formatAppLog("warn", "at utils/theme.js:24", `Theme '${theme}' is not supported.`);
        return;
      }
      const app = getApp();
      if (app && app.$vm && app.$vm.$el) {
        app.$vm.$el.removeAttribute("data-theme");
        if (theme !== "default") {
          app.$vm.$el.setAttribute("data-theme", theme);
        }
      }
      this.currentTheme = theme;
      try {
        uni.setStorageSync("app-theme", theme);
      } catch (e) {
        formatAppLog("error", "at utils/theme.js:47", "Failed to save theme to storage:", e);
      }
    }
    /**
     * 获取当前主题
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
      return this.currentTheme;
    }
    /**
     * 初始化主题
     * 从本地存储加载保存的主题
     */
    init() {
      try {
        const savedTheme = uni.getStorageSync("app-theme") || "default";
        this.switchTheme(savedTheme);
      } catch (e) {
        formatAppLog("error", "at utils/theme.js:69", "Failed to load theme from storage:", e);
        this.switchTheme("default");
      }
    }
    /**
     * 获取所有支持的主题
     * @returns {Array} 主题列表
     */
    getThemes() {
      return this.themes;
    }
  }
  const themeManager = new ThemeManager();
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$2 = {
    // 页面数据
    data() {
      return {
        activeTab: "account",
        // 当前激活的登录标签页 ('account' 或 'phone')
        showPassword: false,
        // 密码是否可见
        countdown: 0,
        // 验证码倒计时
        // 账号登录表单数据
        account: {
          username: "",
          password: ""
        },
        // 手机号登录表单数据
        phone: {
          number: "",
          code: ""
        },
        accountError: "",
        // 账号登录错误信息
        phoneError: ""
        // 手机号登录错误信息
      };
    },
    // 计算属性
    computed: {
      // 验证账号登录表单是否有效
      isValidAccountLogin() {
        return this.account.username && this.account.password;
      },
      // 验证手机号登录表单是否有效
      isValidPhoneLogin() {
        return /^1[3-9]\d{9}$/.test(this.phone.number) && /^\d{6}$/.test(this.phone.code);
      }
    },
    // 页面加载时执行
    mounted() {
      themeManager.init();
    },
    // 页面方法
    methods: {
      /**
       * 切换到账号登录标签页
       */
      switchToAccount() {
        this.activeTab = "account";
      },
      /**
       * 切换到手机号登录标签页
       */
      switchToPhone() {
        this.activeTab = "phone";
      },
      /**
       * 切换密码可见性
       */
      togglePasswordVisibility() {
        this.showPassword = !this.showPassword;
      },
      /**
       * 处理账号登录
       */
      async handleAccountLogin() {
        const validation = ValidationUtil.validateAccountForm(this.account);
        if (!validation.isValid) {
          this.accountError = validation.errors[0];
          return;
        }
        this.accountError = "";
        try {
          uni.showLoading({
            title: "登录中..."
          });
          const result = await LoginService.accountLogin(
            this.account.username,
            this.account.password
          );
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/index/index"
            });
          }, 1e3);
        } catch (error) {
          uni.hideLoading();
          this.accountError = error.message;
        }
      },
      /**
       * 获取验证码
       */
      async getCode() {
        if (!this.phone.number) {
          this.phoneError = "请输入手机号";
          return;
        }
        if (!/^1\d{10}$/.test(this.phone.number)) {
          this.phoneError = "请输入正确的手机号";
          return;
        }
        this.phoneError = "";
        try {
          uni.showLoading({
            title: "发送中..."
          });
          const result = await LoginService.sendVerificationCode(this.phone.number);
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          this.startCountdown();
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message;
        }
      },
      /**
       * 启动验证码倒计时
       */
      startCountdown() {
        this.countdown = 60;
        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--;
          } else {
            clearInterval(timer);
          }
        }, 1e3);
      },
      /**
       * 处理手机号登录
       */
      async handlePhoneLogin() {
        const validation = ValidationUtil.validatePhoneForm(this.phone);
        if (!validation.isValid) {
          this.phoneError = validation.errors[0];
          return;
        }
        this.phoneError = "";
        try {
          uni.showLoading({
            title: "登录中..."
          });
          const result = await LoginService.phoneLogin(
            this.phone.number,
            this.phone.code
          );
          uni.hideLoading();
          uni.showToast({
            title: result.message,
            icon: "success"
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/index/index"
            });
          }, 1e3);
        } catch (error) {
          uni.hideLoading();
          this.phoneError = error.message;
        }
      },
      /**
       * 主题切换事件处理
       */
      onThemeChange(event) {
        const theme = event.target.value;
        themeManager.switchTheme(theme);
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 登录页面容器 "),
        vue.createElementVNode("view", { class: "login-container" }, [
          vue.createCommentVNode(" 登录页面头部 "),
          vue.createElementVNode("view", { class: "login-header" }, [
            vue.createCommentVNode(" 应用Logo "),
            vue.createElementVNode("image", {
              class: "logo",
              src: _imports_0
            }),
            vue.createCommentVNode(" 应用标题 "),
            vue.createElementVNode("text", { class: "app-title" }, "快递员端")
          ]),
          vue.createCommentVNode(" 登录方式切换标签 "),
          vue.createElementVNode("view", { class: "login-tabs" }, [
            vue.createCommentVNode(" 账号登录标签 "),
            vue.createElementVNode(
              "text",
              {
                class: vue.normalizeClass([{ active: $data.activeTab === "account" }, "tab-item"]),
                onClick: _cache[0] || (_cache[0] = (...args) => $options.switchToAccount && $options.switchToAccount(...args))
              },
              " 账号登录 ",
              2
              /* CLASS */
            ),
            vue.createCommentVNode(" 手机号登录标签 "),
            vue.createElementVNode(
              "text",
              {
                class: vue.normalizeClass([{ active: $data.activeTab === "phone" }, "tab-item"]),
                onClick: _cache[1] || (_cache[1] = (...args) => $options.switchToPhone && $options.switchToPhone(...args))
              },
              " 手机号登录 ",
              2
              /* CLASS */
            )
          ]),
          vue.createCommentVNode(" 账号登录表单 "),
          $data.activeTab === "account" ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "login-form"
          }, [
            vue.createCommentVNode(" 用户名输入框组 "),
            vue.createElementVNode("view", { class: "input-group" }, [
              vue.withDirectives(vue.createElementVNode(
                "input",
                {
                  type: "text",
                  "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.account.username = $event),
                  placeholder: "请输入账号",
                  class: "login-input"
                },
                null,
                512
                /* NEED_PATCH */
              ), [
                [vue.vModelText, $data.account.username]
              ])
            ]),
            vue.createCommentVNode(" 密码输入框组 "),
            vue.createElementVNode("view", { class: "input-group password-group" }, [
              vue.withDirectives(vue.createElementVNode("input", {
                type: $data.showPassword ? "text" : "password",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.account.password = $event),
                placeholder: "请输入密码",
                class: "login-input",
                onKeyup: _cache[4] || (_cache[4] = vue.withKeys((...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args), ["enter"]))
              }, null, 40, ["type"]), [
                [vue.vModelDynamic, $data.account.password]
              ]),
              vue.createCommentVNode(" 密码可见性切换图标 "),
              vue.createElementVNode(
                "view",
                {
                  onClick: _cache[5] || (_cache[5] = (...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),
                  class: vue.normalizeClass(["eye-icon", { closed: !$data.showPassword }])
                },
                null,
                2
                /* CLASS */
              )
            ]),
            vue.createCommentVNode(" 登录按钮 "),
            vue.createElementVNode("button", {
              disabled: !$options.isValidAccountLogin,
              onClick: _cache[6] || (_cache[6] = (...args) => $options.handleAccountLogin && $options.handleAccountLogin(...args)),
              class: "login-button"
            }, " 登录 ", 8, ["disabled"]),
            vue.createCommentVNode(" 账号登录错误信息 "),
            $data.accountError ? (vue.openBlock(), vue.createElementBlock(
              "text",
              {
                key: 0,
                class: "error-message"
              },
              vue.toDisplayString($data.accountError),
              1
              /* TEXT */
            )) : vue.createCommentVNode("v-if", true)
          ])) : (vue.openBlock(), vue.createElementBlock(
            vue.Fragment,
            { key: 1 },
            [
              vue.createCommentVNode(" 手机号登录表单 "),
              vue.createElementVNode("view", { class: "login-form" }, [
                vue.createCommentVNode(" 手机号输入框组 "),
                vue.createElementVNode("view", { class: "input-group" }, [
                  vue.withDirectives(vue.createElementVNode(
                    "input",
                    {
                      type: "text",
                      "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $data.phone.number = $event),
                      placeholder: "请输入手机号",
                      maxlength: "11",
                      class: "login-input"
                    },
                    null,
                    512
                    /* NEED_PATCH */
                  ), [
                    [vue.vModelText, $data.phone.number]
                  ])
                ]),
                vue.createCommentVNode(" 验证码输入框组 "),
                vue.createElementVNode("view", { class: "input-group verification-group" }, [
                  vue.withDirectives(vue.createElementVNode(
                    "input",
                    {
                      type: "text",
                      "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.phone.code = $event),
                      placeholder: "请输入验证码",
                      maxlength: "6",
                      class: "login-input verification-input"
                    },
                    null,
                    512
                    /* NEED_PATCH */
                  ), [
                    [vue.vModelText, $data.phone.code]
                  ]),
                  vue.createCommentVNode(" 获取验证码按钮 "),
                  vue.createElementVNode("button", {
                    disabled: $data.countdown > 0,
                    onClick: _cache[9] || (_cache[9] = (...args) => $options.getCode && $options.getCode(...args)),
                    class: "verification-button"
                  }, vue.toDisplayString($data.countdown > 0 ? `${$data.countdown}秒后重发` : "获取验证码"), 9, ["disabled"])
                ]),
                vue.createCommentVNode(" 登录按钮 "),
                vue.createElementVNode("button", {
                  disabled: !$options.isValidPhoneLogin,
                  onClick: _cache[10] || (_cache[10] = (...args) => $options.handlePhoneLogin && $options.handlePhoneLogin(...args)),
                  class: "login-button"
                }, " 登录 ", 8, ["disabled"]),
                vue.createCommentVNode(" 手机号登录错误信息 "),
                $data.phoneError ? (vue.openBlock(), vue.createElementBlock(
                  "text",
                  {
                    key: 0,
                    class: "error-message"
                  },
                  vue.toDisplayString($data.phoneError),
                  1
                  /* TEXT */
                )) : vue.createCommentVNode("v-if", true)
              ])
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )),
          vue.createCommentVNode(" 主题切换选择器 "),
          vue.createElementVNode("view", { class: "theme-selector-container" }, [
            vue.createElementVNode(
              "select",
              {
                onChange: _cache[11] || (_cache[11] = (...args) => $options.onThemeChange && $options.onThemeChange(...args)),
                class: "theme-selector"
              },
              [
                vue.createElementVNode("option", { value: "default" }, "默认主题"),
                vue.createElementVNode("option", { value: "dark" }, "暗色主题"),
                vue.createElementVNode("option", { value: "green" }, "绿色主题"),
                vue.createElementVNode("option", { value: "red" }, "紫色主题")
              ],
              32
              /* NEED_HYDRATION */
            )
          ])
        ])
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__scopeId", "data-v-e4e4508d"], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/login/login.vue"]]);
  function onThemeChange(event) {
    const theme = event.target.value;
    themeManager.switchTheme(theme);
  }
  const _sfc_main$1 = {
    __name: "index",
    setup(__props, { expose: __expose }) {
      __expose();
      const title = vue.ref("首页");
      vue.onMounted(() => {
      });
      const __returned__ = { onThemeChange, title, ref: vue.ref, onMounted: vue.onMounted, get themeManager() {
        return themeManager;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      vue.Fragment,
      null,
      [
        vue.createCommentVNode(" 首页容器 "),
        vue.createElementVNode("view", { class: "content" }, [
          vue.createCommentVNode(" 应用Logo "),
          vue.createElementVNode("image", {
            class: "logo",
            src: _imports_0
          }),
          vue.createCommentVNode(" 标题区域 "),
          vue.createElementVNode("view", { class: "text-area" }, [
            vue.createElementVNode(
              "text",
              { class: "title" },
              vue.toDisplayString($setup.title),
              1
              /* TEXT */
            )
          ]),
          vue.createCommentVNode(" 主题切换选择器 "),
          vue.createElementVNode("view", { class: "theme-selector-container" }, [
            vue.createElementVNode(
              "select",
              {
                onChange: $setup.onThemeChange,
                class: "theme-selector"
              },
              [
                vue.createElementVNode("option", { value: "default" }, "默认主题"),
                vue.createElementVNode("option", { value: "dark" }, "暗色主题"),
                vue.createElementVNode("option", { value: "green" }, "绿色主题"),
                vue.createElementVNode("option", { value: "red" }, "紫色主题")
              ],
              32
              /* NEED_HYDRATION */
            )
          ])
        ])
      ],
      2112
      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
    );
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/pages/index/index.vue"]]);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/index/index", PagesIndexIndex);
  const _sfc_main = {
    onLaunch() {
      formatAppLog("log", "at App.vue:8", "App Launch");
      themeManager.init();
    },
    onShow() {
      formatAppLog("log", "at App.vue:14", "App Show");
    },
    onHide() {
      formatAppLog("log", "at App.vue:18", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "C:/Users/<USER>/Documents/HBuilderProjects/sl-express-courier/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
