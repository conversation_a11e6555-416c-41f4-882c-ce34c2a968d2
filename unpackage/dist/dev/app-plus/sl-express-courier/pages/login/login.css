/* 
 * 登录页面变量文件
 * 定义登录页面使用的所有CSS变量
 */

/* 
 * 登录页面样式文件
 * 包含登录页面的所有样式定义
 */
[data-v-c56f49e1]:root {
  /* 主题颜色 */
  --primary-color: #007AFF;
  --secondary-color: #4B9FFF;
  
  /* 背景颜色 */
  --bg-white: #FFFFFF;
  --bg-light: rgba(255, 255, 255, 0.9);
  --bg-gray: #CCCCCC;
  
  /* 文本颜色 */
  --text-white: #FFFFFF;
  --text-gray: #666666;
  --text-error: #FF4D4F;
  
  /* 边框颜色 */
  --border-gray: #E5E5E5;
  
  /* 阴影颜色 */
  --shadow-blue: rgba(0, 0, 0, 0.2);
  --shadow-blue-light: rgba(0, 0, 0, 0.1);
  --shadow-blue-lighter: rgba(0, 122, 255, 0.3);
}

/* 登录容器 */
.login-container[data-v-c56f49e1] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 1.25rem;
  background: linear-gradient(to bottom, #007AFF, #4B9FFF);
  min-height: 100vh;
  padding-top: 4.6875rem;
}

/* 登录头部 */
.login-header[data-v-c56f49e1] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2.5rem;
}

/* Logo图标 */
.logo[data-v-c56f49e1] {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  box-shadow: 0 0.125rem 0.625rem var(--shadow-blue);
  margin-bottom: 0.9375rem;
  background-color: #FFFFFF;
  padding: 0.3125rem;
}

/* 应用标题 */
.app-title[data-v-c56f49e1] {
  font-size: 1.5rem;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
}

/* 登录标签页容器 */
.login-tabs[data-v-c56f49e1] {
  display: flex;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 1.875rem;
  padding: 0.3125rem;
  margin-bottom: 1.5625rem;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 18.75rem;
}

/* 标签项 */
.tab-item[data-v-c56f49e1] {
  flex: 1;
  min-width: 3.75rem;
  text-align: center;
  padding: 0.78125rem 1.25rem;
  font-size: 0.875rem;
  color: #666666;
  border-radius: 1.875rem;
  transition: all 0.3s;
  cursor: pointer;
  box-sizing: border-box;
}

/* 激活的标签项 */
.tab-item.active[data-v-c56f49e1] {
  background-color: #007AFF;
  color: #FFFFFF;
  font-weight: bold;
}

/* 登录表单 */
.login-form[data-v-c56f49e1] {
  width: 100%;
  max-width: 18.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.625rem;
  padding: 1.25rem;
  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.15);
}

/* 输入框组 */
.input-group[data-v-c56f49e1] {
  position: relative;
  margin-bottom: 0.9375rem;
}

/* 登录输入框 */
.login-input[data-v-c56f49e1] {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.9375rem;
  border: 0.0625rem solid #E5E5E5;
  border-radius: 0.46875rem;
  font-size: 0.875rem;
  box-sizing: border-box;
  background-color: #FFFFFF;
}

/* 输入框聚焦状态 */
.login-input[data-v-c56f49e1]:focus {
  border-color: #007AFF;
}

/* 密码组容器 */
.password-group[data-v-c56f49e1] {
  display: flex;
  align-items: center;
}

/* 密码切换按钮 */
.password-toggle[data-v-c56f49e1] {
  position: absolute;
  right: 0.78125rem;
  color: #007AFF;
  font-size: 0.8125rem;
  z-index: 2;
  background-color: #FFFFFF;
  padding: 0 0.3125rem;
  cursor: pointer;
}

/* 眼睛图标容器 */
.eye-icon[data-v-c56f49e1] {
  position: absolute;
  right: 0.78125rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background-color: #FFFFFF;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 眼睛图标样式 */
.eye-icon[data-v-c56f49e1]::before {
  content: "";
  display: inline-block;
  width: 0.9375rem;
  height: 0.625rem;
  border: 0.0625rem solid #007AFF;
  border-radius: 50%;
  position: relative;
}

/* 眼睛瞳孔 */
.eye-icon[data-v-c56f49e1]::after {
  content: "";
  display: inline-block;
  width: 0.25rem;
  height: 0.25rem;
  background-color: #007AFF;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 闭眼图标样式（斜线） */
.eye-icon.closed[data-v-c56f49e1]::before {
  content: "";
  display: inline-block;
  width: 0.9375rem;
  height: 0.625rem;
  border: 0.0625rem solid #007AFF;
  border-radius: 50%;
  position: relative;
}
.eye-icon.closed[data-v-c56f49e1]::after {
  content: "";
  display: block;
  width: 0.0625rem;
  height: 0.9375rem;
  background-color: #007AFF;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 0;
}

/* 验证码组容器 */
.verification-group[data-v-c56f49e1] {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.625rem;
}

/* 验证码输入框 */
.verification-input[data-v-c56f49e1] {
  flex: 1;
}

/* 验证码按钮 */
.verification-button[data-v-c56f49e1] {
  width: 6.25rem;
  height: 2.5rem;
  background-color: #007AFF;
  color: #FFFFFF;
  border: none;
  border-radius: 0.46875rem;
  font-size: 0.8125rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  padding: 0;
  box-sizing: border-box;
}

/* 验证码按钮禁用状态 */
.verification-button[data-v-c56f49e1]:disabled {
  background-color: #CCCCCC;
  cursor: not-allowed;
}

/* 登录按钮 */
.login-button[data-v-c56f49e1] {
  width: 100%;
  height: 2.5rem;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 0.46875rem;
  font-size: 1rem;
  font-weight: bold;
  margin-top: 0.625rem;
  box-shadow: 0 0.125rem 0.3125rem rgba(0, 122, 255, 0.3);
  cursor: pointer;
}

/* 登录按钮禁用状态 */
.login-button[data-v-c56f49e1]:disabled {
  background-color: #CCCCCC;
  cursor: not-allowed;
}

/* 错误信息 */
.error-message[data-v-c56f49e1] {
  color: #FF4D4F;
  font-size: 0.75rem;
  margin-top: 0.625rem;
  text-align: center;
}