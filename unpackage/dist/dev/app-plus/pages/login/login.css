/* 
 * 登录页面样式文件
 * 定义登录页面使用的所有CSS样式
 */
[data-v-e4e4508d]:root {
  /* 主题颜色 */
  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  --primary-hover: #2980b9;      /* 主色调悬停状态 */
  
  /* 背景颜色 */
  --bg-white: #ffffff;           /* 白色背景 */
  --bg-light: #f8f9fa;           /* 浅色半透明背景 */
  --bg-gray: #bdc3c7;            /* 灰色背景 - 禁用状态按钮 */
  --bg-dark: #ecf0f1;            /* 深灰色背景 */
  
  /* 文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-dark: #2c3e50;          /* 深色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */
  
  /* 边框颜色 */
  --border-color: #dcdde1;       /* 输入框边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
  --shadow-light: rgba(0, 0, 0, 0.1);     /* 浅色阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 登录容器 */
.login-container[data-v-e4e4508d] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  min-height: 100vh;
  padding-top: 3.125rem;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

/* 登录容器装饰元素 */
.login-container[data-v-e4e4508d]::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* 登录头部 */
.login-header[data-v-e4e4508d] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.875rem;
  position: relative;
  z-index: 1;
}

/* Logo图标 */
.logo[data-v-e4e4508d] {
  width: 5rem;
  height: 5rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.3125rem 0.9375rem var(--shadow-dark);
  margin-bottom: 0.9375rem;
  background-color: var(--bg-white);
  padding: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 2.5rem;
  font-weight: bold;
}

/* 应用标题 */
.app-title[data-v-e4e4508d] {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
  letter-spacing: 0.03125rem;
}

/* 登录标签页容器 */
.login-tabs[data-v-e4e4508d] {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 1.875rem;
  padding: 0.3125rem;
  margin-bottom: 1.5625rem;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 18.75rem;
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  position: relative;
  z-index: 1;
}

/* 标签项 */
.tab-item[data-v-e4e4508d] {
  flex: 1;
  min-width: 3.75rem;
  text-align: center;
  padding: 0.78125rem 1.25rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 1.875rem;
  transition: all 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
  font-weight: 500;
}

/* 激活的标签项 */
.tab-item.active[data-v-e4e4508d] {
  background-color: var(--bg-white);
  color: var(--primary-color);
  font-weight: 700;
  box-shadow: 0 0.125rem 0.3125rem var(--shadow-primary);
}

/* 登录表单 */
.login-form[data-v-e4e4508d] {
  width: 100%;
  max-width: 18.75rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.625rem;
  padding: 1.25rem;
  box-shadow: 0 0.46875rem 1.09375rem rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  position: relative;
  z-index: 1;
}

/* 输入框组 */
.input-group[data-v-e4e4508d] {
  position: relative;
  margin-bottom: 0.9375rem;
}

/* 登录输入框 */
.login-input[data-v-e4e4508d] {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.9375rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.46875rem;
  font-size: 0.875rem;
  box-sizing: border-box;
  background-color: var(--bg-white);
  color: var(--text-dark);
  transition: all 0.3s ease;
}

/* 输入框聚焦状态 */
.login-input[data-v-e4e4508d]:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 0.09375rem var(--shadow-primary);
  outline: none;
}

/* 密码组容器 */
.password-group[data-v-e4e4508d] {
  display: flex;
  align-items: center;
}

/* 密码切换按钮 */
.password-toggle[data-v-e4e4508d] {
  position: absolute;
  right: 0.78125rem;
  color: var(--primary-color);
  font-size: 0.8125rem;
  z-index: 2;
  background-color: var(--bg-white);
  padding: 0 0.3125rem;
  cursor: pointer;
}

/* 眼睛图标容器 */
.eye-icon[data-v-e4e4508d] {
  position: absolute;
  right: 0.78125rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  background-color: #FFFFFF;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
}

/* 眼睛图标样式 */
.eye-icon[data-v-e4e4508d]::before {
  content: "";
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border: 0.0625rem solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}

/* 眼睛瞳孔 */
.eye-icon[data-v-e4e4508d]::after {
  content: "";
  display: inline-block;
  width: 0.25rem;
  height: 0.25rem;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 闭眼图标样式（斜线） */
.eye-icon.closed[data-v-e4e4508d]::before {
  content: "";
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border: 0.0625rem solid var(--primary-color);
  border-radius: 50%;
  position: relative;
}
.eye-icon.closed[data-v-e4e4508d]::after {
  content: "";
  display: block;
  width: 0.0625rem;
  height: 0.9375rem;
  background-color: var(--primary-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  border-radius: 0;
}

/* 验证码组容器 */
.verification-group[data-v-e4e4508d] {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.625rem;
}

/* 验证码输入框 */
.verification-input[data-v-e4e4508d] {
  flex: 1;
}

/* 验证码按钮 */
.verification-button[data-v-e4e4508d] {
  width: 6.25rem;
  height: 2.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  color: var(--text-white);
  border: none;
  border-radius: 0.46875rem;
  font-size: 0.8125rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  padding: 0;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 0.125rem 0.3125rem var(--shadow-primary);
  transition: all 0.3s ease;
}

/* 验证码按钮悬停状态 */
.verification-button[data-v-e4e4508d]:hover:not(:disabled) {
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.1875rem 0.46875rem var(--shadow-primary);
}

/* 验证码按钮禁用状态 */
.verification-button[data-v-e4e4508d]:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 登录按钮 */
.login-button[data-v-e4e4508d] {
  width: 100%;
  height: 2.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: var(--text-white);
  border: none;
  border-radius: 0.46875rem;
  font-size: 1rem;
  font-weight: 700;
  margin-top: 0.625rem;
  box-shadow: 0 0.125rem 0.46875rem var(--shadow-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.0625rem;
}

/* 登录按钮悬停状态 */
.login-button[data-v-e4e4508d]:hover:not(:disabled) {
  transform: translateY(-0.09375rem);
  box-shadow: 0 0.21875rem 0.625rem var(--shadow-dark);
}

/* 登录按钮禁用状态 */
.login-button[data-v-e4e4508d]:disabled {
  background: linear-gradient(to right, var(--bg-gray), #95a5a6);
  cursor: not-allowed;
  box-shadow: none;
}

/* 错误信息 */
.error-message[data-v-e4e4508d] {
  color: var(--text-error);
  font-size: 0.75rem;
  margin-top: 0.625rem;
  text-align: center;
  font-weight: 500;
}

/* 主题切换容器 */
.theme-selector-container[data-v-e4e4508d] {
  position: fixed;
  bottom: 0.625rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

/* 主题选择器 */
.theme-selector[data-v-e4e4508d] {
  padding: 0.46875rem 0.9375rem;
  font-size: 0.875rem;
  border-radius: 1.5625rem;
  border: none;
  background-color: var(--bg-white);
  color: var(--text-color, #2c3e50);
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-weight: 500;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.46875rem center;
  background-size: 0.5625rem;
  padding-right: 1.5625rem;
  cursor: pointer;
}

/* 主题选择器悬停效果 */
.theme-selector[data-v-e4e4508d]:hover {
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.2);
}

/* 主题选择器选项样式 */
.theme-selector option[data-v-e4e4508d] {
  padding: 0.3125rem;
  font-weight: 500;
}