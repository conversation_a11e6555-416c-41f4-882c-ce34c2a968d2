
/* 页面内容容器 */
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.25rem;
  min-height: 100vh;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(135deg, var(--primary-color, #3498db), var(--secondary-color, #2c3e50));
  position: relative;
}

/* 装饰元素 */
.content::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
  z-index: 0;
}

/* Logo样式 */
.logo {
  height: 6.25rem;
  width: 6.25rem;
  margin: 1.25rem;
  border-radius: 0.9375rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.2);
  background-color: var(--bg-white, #ffffff);
  padding: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color, #3498db);
  font-size: 2.5rem;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* 文本区域 */
.text-area {
  display: flex;
  justify-content: center;
  margin: 1.25rem;
  position: relative;
  z-index: 1;
}

/* 页面标题 */
.title {
  font-size: 1.125rem;
  color: var(--text-white, #ffffff);
  font-weight: bold;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2);
}

/* 主题切换容器 */
.theme-selector-container {
  position: fixed;
  bottom: 0.625rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

/* 主题选择器 */
.theme-selector {
  padding: 0.46875rem 0.9375rem;
  font-size: 0.875rem;
  border-radius: 1.5625rem;
  border: none;
  background-color: var(--bg-white, rgba(255, 255, 255, 0.9));
  color: var(--text-color, #2c3e50);
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-weight: 500;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.46875rem center;
  background-size: 0.5625rem;
  padding-right: 1.5625rem;
  cursor: pointer;
}

/* 主题选择器悬停效果 */
.theme-selector:hover {
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.2);
}

/* 主题选择器选项样式 */
.theme-selector option {
  padding: 0.3125rem;
  font-weight: 500;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 768px) {
.content {
    padding: 0.625rem;
}
.logo {
    height: 4.6875rem;
    width: 4.6875rem;
}
.title {
    font-size: 1rem;
}
}
