<template>
  <!-- 登录页面容器 -->
  <view class="login-container">
    <!-- 主题切换选择器 -->
    <view class="theme-selector-container top-right">
      <select @change="onThemeChange" class="theme-selector">
        <option value="default">默认主题</option>
        <option value="dark">暗色主题</option>
        <option value="green">绿色主题</option>
        <option value="red">紫色主题</option>
      </select>
    </view>
    <!-- 应用Logo -->
    <image class="logo" src="/static/logo.png"></image>
    <!-- 应用标题 -->
    <text class="app-title">快递员端</text>
    
    <!-- 登录方式切换标签 -->
    <view class="login-tabs">
      <text :class="{ active: activeTab === 'account' }" @click="switchToAccount">账号登录</text>
      <text :class="{ active: activeTab === 'phone' }" @click="switchToPhone">手机号登录</text>
    </view>
    <!-- 账号登录表单 -->
    <view v-if="activeTab === 'account'" class="login-form">
      <!-- 用户名输入框组 -->
      <view class="input-group">
        <input type="text" v-model="account.username" placeholder="请输入账号" class="login-input"/>
      </view>
      <!-- 密码输入框组 -->
      <view class="input-group password-group">
        <input :type="showPassword ? 'text' : 'password'" v-model="account.password" placeholder="请输入密码" class="login-input"/>
        <view @click="togglePasswordVisibility" class="eye-icon" :class="{ closed: !showPassword }"></view>
      </view>
      <!-- 登录按钮 -->
      <button :disabled="!isValidAccountLogin" @click="handleAccountLogin" class="login-button">登录</button>
      <!-- 账号登录错误信息 -->
      <text v-if="accountError" class="error-message">{{ accountError }}</text>
    </view>
    <!-- 手机号登录表单 -->
    <view v-else class="login-form">
      <!-- 手机号输入框组 -->
      <view class="input-group">
        <input type="text" v-model="phone.number" placeholder="请输入手机号" maxlength="11" class="login-input"/>
      </view>
      <!-- 验证码输入框组 -->
      <view class="input-group verification-group">
        <input type="text" v-model="phone.code" placeholder="请输入验证码" maxlength="6" class="login-input verification-input"/>
        <button :disabled="countdown > 0" @click="getCode" class="verification-button">
          {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
        </button>
      </view>
      <!-- 登录按钮 -->
      <button :disabled="!isValidPhoneLogin" @click="handlePhoneLogin" class="login-button">登录</button>
      <!-- 手机号登录错误信息 -->
      <text v-if="phoneError" class="error-message">{{ phoneError }}</text>
    </view>
  </view>
</template>

<script>
</script>

<style scoped>
/* 添加注释 */
/* 主题选择器容器 - 右上角 */
.theme-selector-container.top-right {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
}
/* ... existing code ... */
</style>