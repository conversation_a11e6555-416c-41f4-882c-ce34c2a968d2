/* 
 * 全局主题变量定义
 * 定义应用中使用的所有CSS变量，便于统一管理和主题切换
 */

/* 默认主题 - 现代蓝主题 */
:root {
  /* 主题色 */
  --primary-color: #3498db;      /* 主色调 - 现代蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  
  /* 文本颜色 */
  --text-color: #2c3e50;         /* 主要文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-light: #bdc3c7;         /* 浅灰色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */
  
  /* 背景颜色 */
  --bg-color: #ffffff;           /* 主要背景色 */
  --bg-light: #f8f9fa;           /* 浅色背景 */
  --bg-dark: #ecf0f1;            /* 深色背景 */
  --bg-overlay: rgba(44, 62, 80, 0.9); /* 覆盖层背景 */
  
  /* 边框颜色 */
  --border-color: #dcdde1;       /* 边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.1);     /* 普通阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.2);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 暗色主题 - 深邃夜空 */
[data-theme="dark"] {
  --primary-color: #3498db;      /* 主色调 - 蓝色 */
  --secondary-color: #2c3e50;    /* 辅助色调 - 深蓝灰色 */
  --accent-color: #1abc9c;       /* 强调色 - 青绿色 */
  
  /* 文本颜色 */
  --text-color: #ecf0f1;         /* 主要文本颜色 */
  --text-white: #2c3e50;         /* 白色文本 */
  --text-gray: #95a5a6;          /* 灰色文本 */
  --text-light: #7f8c8d;         /* 浅灰色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */
  
  /* 背景颜色 */
  --bg-color: #1a1a2e;           /* 主要背景色 */
  --bg-light: #16213e;           /* 浅色背景 */
  --bg-dark: #0f3460;            /* 深色背景 */
  --bg-overlay: rgba(26, 26, 46, 0.9); /* 覆盖层背景 */
  
  /* 边框颜色 */
  --border-color: #2c3e50;       /* 边框颜色 */
  --border-focus: #3498db;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(0, 0, 0, 0.3);     /* 普通阴影 */
  --shadow-dark: rgba(0, 0, 0, 0.5);      /* 深色阴影 */
  --shadow-primary: rgba(52, 152, 219, 0.3); /* 主题色阴影 */
  --shadow-accent: rgba(26, 188, 156, 0.3);  /* 强调色阴影 */
}

/* 绿色主题 - 自然清新 */
[data-theme="green"] {
  --primary-color: #2ecc71;      /* 主色调 - 翠绿色 */
  --secondary-color: #27ae60;    /* 辅助色调 - 深绿色 */
  --accent-color: #f1c40f;       /* 强调色 - 金黄色 */
  
  /* 文本颜色 */
  --text-color: #2c3e50;         /* 主要文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-light: #bdc3c7;         /* 浅灰色文本 */
  --text-error: #e74c3c;         /* 错误文本颜色 */
  
  /* 背景颜色 */
  --bg-color: #ffffff;           /* 主要背景色 */
  --bg-light: #f8fff9;           /* 浅色背景 */
  --bg-dark: #e8f8f5;            /* 深色背景 */
  --bg-overlay: rgba(39, 174, 96, 0.9); /* 覆盖层背景 */
  
  /* 边框颜色 */
  --border-color: #d1f2eb;       /* 边框颜色 */
  --border-focus: #2ecc71;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(46, 204, 113, 0.1);     /* 普通阴影 */
  --shadow-dark: rgba(46, 204, 113, 0.2);     /* 深色阴影 */
  --shadow-primary: rgba(46, 204, 113, 0.3);  /* 主题色阴影 */
  --shadow-accent: rgba(241, 196, 15, 0.3);   /* 强调色阴影 */
}

/* 紫色主题 - 优雅浪漫 */
[data-theme="red"] {
  --primary-color: #9b59b6;      /* 主色调 - 紫色 */
  --secondary-color: #8e44ad;    /* 辅助色调 - 深紫色 */
  --accent-color: #e74c3c;       /* 强调色 - 红色 */
  
  /* 文本颜色 */
  --text-color: #2c3e50;         /* 主要文本颜色 */
  --text-white: #ffffff;         /* 白色文本 */
  --text-gray: #7f8c8d;          /* 灰色文本 */
  --text-light: #bdc3c7;         /* 浅灰色文本 */
  --text-error: #c0392b;         /* 错误文本颜色 */
  
  /* 背景颜色 */
  --bg-color: #ffffff;           /* 主要背景色 */
  --bg-light: #fdf7ff;           /* 浅色背景 */
  --bg-dark: #f5eef8;            /* 深色背景 */
  --bg-overlay: rgba(142, 68, 173, 0.9); /* 覆盖层背景 */
  
  /* 边框颜色 */
  --border-color: #e8daef;       /* 边框颜色 */
  --border-focus: #9b59b6;       /* 聚焦边框颜色 */
  
  /* 阴影颜色 */
  --shadow-color: rgba(155, 89, 182, 0.1);    /* 普通阴影 */
  --shadow-dark: rgba(155, 89, 182, 0.2);     /* 深色阴影 */
  --shadow-primary: rgba(155, 89, 182, 0.3);  /* 主题色阴影 */
  --shadow-accent: rgba(231, 76, 60, 0.3);    /* 强调色阴影 */
}

/* 全局样式重置 */
html, body {
  margin: 0;              /* 移除默认外边距 */
  padding: 0;             /* 移除默认内边距 */
  overflow: hidden;       /* 隐藏滚动条 */
  width: 100%;            /* 宽度占满屏幕 */
  height: 100%;           /* 高度占满屏幕 */
  box-sizing: border-box; /* 使用边框盒模型 */
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; /* 设置默认字体 */
}

/* 全局滚动条隐藏 */
::-webkit-scrollbar {
  display: none;
}

/* 确保所有元素使用边框盒模型 */
*, *::before, *::after {
  box-sizing: inherit;
}