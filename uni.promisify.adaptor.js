/**
 * UniApp Promise适配器
 * 用于统一处理异步API返回值格式
 * 将回调形式的API转换为Promise形式
 */

// 添加拦截器统一处理异步API返回值
uni.addInterceptor({
  /**
   * 处理异步API返回值
   * @param {Object|Function|Promise} res - API返回值
   * @returns {Promise} Promise对象
   */
  returnValue (res) {
    // 如果返回值不是Promise对象，直接返回
    if (!(!!res && (typeof res === "object" || typeof res === "function") && typeof res.then === "function")) {
      return res;
    }
    
    // 将回调形式的API转换为Promise形式
    return new Promise((resolve, reject) => {
      res.then((res) => {
        // 如果没有返回值，直接resolve
        if (!res) return resolve(res) 
        // 如果第一个参数是错误信息，则reject，否则resolve第二个参数
        return res[0] ? reject(res[0]) : resolve(res[1])
      });
    });
  },
});